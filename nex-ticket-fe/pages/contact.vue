<script lang="ts" setup>
import { SentIcon } from 'hugeicons-vue'
import { da } from 'vuetify/lib/locale/index.mjs'
import { boolean as yupBoolean, object as yupObject, string as yupString } from 'yup'

const { t } = useI18n()

const schema = computed(() => {
  return toTypedSchema(
    yupObject({
      name: yupString().required().min(5).max(25).label(t('auth.fields.name')),
      email: yupString().email().required().label(t('auth.fields.email')),
      subject: yupString().required().min(5).max(20).label(t('auth.fields.subject')),
      message: yupString().required().min(10).max(2500).label(t('auth.fields.contact_message')),
      user_agreement: yupBoolean().required().oneOf([true]),
    }),
  )
})

const feedback = useFeedback()

const { validate } = useForm({
  validationSchema: schema,
})

const name = useField<string>('name')
const email = useField<string>('email')
const subject = useField<string>('subject')
const message = useField<string>('message')
const user_agreement = useField<boolean>('user_agreement')
const serverResponse = ref<null | any>(null)

const validatedInput = ref<boolean>(false)

async function sendMessage() {
  const isValid = await validate()
  validatedInput.value = true
  if (isValid.valid) {
    try {
      const { error: fetchError } = await useAPI('/api/public/contact_support', {
        method: 'POST',
        body: {
          name: name.value.value,
          email: email.value.value,
          subject: subject.value.value,
          message: message.value.value,
        },
      })
      if (fetchError.value) {
        feedback.error('Error creating event:', { level: 'error', rollbar: true, extras: fetchError.value })
        serverResponse.value = fetchError.value.message
      }
    }
    catch (err: any) {
      feedback.error('Unexpected error:', { level: 'error', rollbar: true, extras: err })
      serverResponse.value = err.message
    }
  }
}
</script>

<template>
  <div class="w-full px-4 max-w-[95vw] lg:max-w-[80rem] mx-auto">
    <div class="flex flex-col gap-8 md:gap-12">
      <!-- Header Section -->
      <div class="flex flex-col gap-4 text-center">
        <h1 class="text-3xl-bold md:text-4xl-bold text-pie-700">
          {{ t('info.contact.contact_ticketpie_support') }}
        </h1>
        <p class="text-base-medium md:text-lg-medium text-slate-600 max-w-2xl mx-auto">
          {{ t('info.contact.details') }}
        </p>
      </div>

      <!-- Contact Form -->
      <div class="w-full max-w-4xl mx-auto">
        <div class="bg-pie-25 border border-slate-300 shadow-lg rounded-2xl p-6 md:p-8">
          <div class="flex flex-col gap-6">
            <!-- Name and Email Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <v-text-field
                v-model="name.value.value"
                variant="outlined"
                :label="t('auth.fields.name')"
                append-inner-icon="mdi-account"
                :error-messages="name.errorMessage.value"
                class="w-full"
              />
              <v-text-field
                v-model="email.value.value"
                variant="outlined"
                :label="t('auth.fields.email')"
                append-inner-icon="mdi-email"
                :error-messages="email.errorMessage.value"
                class="w-full"
              />
            </div>

            <!-- Subject -->
            <v-text-field
              v-model="subject.value.value"
              variant="outlined"
              :label="t('auth.fields.subject')"
              :error-messages="subject.errorMessage.value"
              class="w-full"
            />

            <!-- Message -->
            <div class="flex flex-col gap-2">
              <label class="text-base-medium text-slate-600">
                {{ t('info.contact.message') }}
              </label>
              <v-textarea
                id="message"
                v-model="message.value.value"
                :label="t('auth.fields.contact_message')"
                :error-messages="message.errorMessage.value"
                variant="outlined"
                placeholder="Enter your message"
                rows="6"
                class="w-full"
              />
            </div>

            <!-- User Agreement -->
            <div class="flex flex-col gap-4">
              <NexCheckbox
                id="user_agreement"
                v-model="user_agreement.value.value"
                :text="t('public.event_info.contact_form.user_agreement')"
                :validated="validatedInput"
                :required="true"
              />
            </div>

            <!-- Submit Button -->
            <div class="flex justify-center pt-4">
              <NexButton
                :text="$t('public.event_info.contact_form.buttons.send')"
                variant="primary"
                :append-icon="SentIcon"
                :paddingx="8"
                :paddingy="4"
                text-style="text-lg-bold"
                class="md:w-auto w-full"
                @click="sendMessage"
              />
            </div>

            <!-- Server Response -->
            <div v-if="serverResponse" class="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p class="text-red-600 text-sm-medium">
                {{ serverResponse }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>

</style>
